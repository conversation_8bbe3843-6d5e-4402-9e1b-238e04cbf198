2025-07-09 18:05:31.043 -07:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-09 18:05:31.168 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "EmailAddress" TEXT NOT NULL,
    "ImapServer" TEXT NOT NULL,
    "ImapPort" INTEGER NOT NULL,
    "UseSsl" INTEGER NOT NULL,
    "Username" TEXT NOT NULL,
    "Password" TEXT NOT NULL,
    "IsEnabled" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "CalendarEvents" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
    "GoogleId" TEXT NOT NULL,
    "CalendarId" TEXT NOT NULL,
    "Summary" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "Location" TEXT NOT NULL,
    "StartDateTime" TEXT NULL,
    "EndDateTime" TEXT NULL,
    "IsAllDay" INTEGER NOT NULL,
    "TimeZone" TEXT NOT NULL,
    "Status" TEXT NOT NULL,
    "Visibility" TEXT NOT NULL,
    "Attendees" TEXT NOT NULL,
    "Organizer" TEXT NOT NULL,
    "Creator" TEXT NOT NULL,
    "RecurrenceRule" TEXT NOT NULL,
    "RecurringEventId" TEXT NOT NULL,
    "HtmlLink" TEXT NOT NULL,
    "HangoutLink" TEXT NOT NULL,
    "ConferenceData" TEXT NOT NULL,
    "Reminders" TEXT NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    "ETag" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Contacts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
    "GoogleId" TEXT NOT NULL,
    "DisplayName" TEXT NOT NULL,
    "GivenName" TEXT NOT NULL,
    "FamilyName" TEXT NOT NULL,
    "MiddleName" TEXT NOT NULL,
    "EmailAddresses" TEXT NOT NULL,
    "PhoneNumbers" TEXT NOT NULL,
    "Addresses" TEXT NOT NULL,
    "Organizations" TEXT NOT NULL,
    "PhotoUrl" TEXT NOT NULL,
    "Notes" TEXT NOT NULL,
    "Birthday" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    "ETag" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "GoogleApiSettings" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
    "ClientId" TEXT NOT NULL,
    "ClientSecret" TEXT NOT NULL,
    "AccessToken" TEXT NOT NULL,
    "RefreshToken" TEXT NOT NULL,
    "TokenExpiresAt" TEXT NULL,
    "Scopes" TEXT NOT NULL,
    "ContactsSyncEnabled" INTEGER NOT NULL,
    "CalendarSyncEnabled" INTEGER NOT NULL,
    "ContactsSyncIntervalMinutes" INTEGER NOT NULL,
    "CalendarSyncIntervalMinutes" INTEGER NOT NULL,
    "LastContactsSync" TEXT NULL,
    "LastCalendarSync" TEXT NULL,
    "ContactsSyncToken" TEXT NOT NULL,
    "CalendarSyncToken" TEXT NOT NULL,
    "IsAuthenticated" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL
);
2025-07-09 18:05:31.170 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Folders" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Folders" PRIMARY KEY AUTOINCREMENT,
    "AccountId" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "ParentFolderId" INTEGER NULL,
    "Type" INTEGER NOT NULL,
    "UnreadCount" INTEGER NOT NULL,
    "TotalCount" INTEGER NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    CONSTRAINT "FK_Folders_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Folders_Folders_ParentFolderId" FOREIGN KEY ("ParentFolderId") REFERENCES "Folders" ("Id") ON DELETE RESTRICT
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Messages" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Messages" PRIMARY KEY AUTOINCREMENT,
    "MessageId" TEXT NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "FolderId" INTEGER NULL,
    "Subject" TEXT NOT NULL,
    "FromAddress" TEXT NOT NULL,
    "FromName" TEXT NOT NULL,
    "ToAddresses" TEXT NOT NULL,
    "CcAddresses" TEXT NOT NULL,
    "BccAddresses" TEXT NOT NULL,
    "DateSent" TEXT NOT NULL,
    "DateReceived" TEXT NOT NULL,
    "TextBody" TEXT NOT NULL,
    "HtmlBody" TEXT NOT NULL,
    "IsRead" INTEGER NOT NULL,
    "IsFlagged" INTEGER NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Size" INTEGER NOT NULL,
    "HasAttachments" INTEGER NOT NULL,
    CONSTRAINT "FK_Messages_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Messages_Folders_FolderId" FOREIGN KEY ("FolderId") REFERENCES "Folders" ("Id") ON DELETE SET NULL
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Attachments" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Attachments" PRIMARY KEY AUTOINCREMENT,
    "MessageId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "ContentType" TEXT NOT NULL,
    "Size" INTEGER NOT NULL,
    "ContentId" TEXT NOT NULL,
    "IsInline" INTEGER NOT NULL,
    "Data" BLOB NULL,
    "FilePath" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_Attachments_Messages_MessageId" FOREIGN KEY ("MessageId") REFERENCES "Messages" ("Id") ON DELETE CASCADE
);
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_EmailAddress" ON "Accounts" ("EmailAddress");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Attachments_MessageId" ON "Attachments" ("MessageId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_CalendarEvents_GoogleId_CalendarId" ON "CalendarEvents" ("GoogleId", "CalendarId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Contacts_GoogleId" ON "Contacts" ("GoogleId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Folders_AccountId_FullName" ON "Folders" ("AccountId", "FullName");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Folders_ParentFolderId" ON "Folders" ("ParentFolderId");
2025-07-09 18:05:31.171 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Messages_AccountId_MessageId" ON "Messages" ("AccountId", "MessageId");
2025-07-09 18:05:31.172 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Messages_FolderId" ON "Messages" ("FolderId");
2025-07-09 18:05:32.350 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:05:32.423 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:05:32.427 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:05:32.451 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:05:32.482 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:05:33.596 -07:00 [INF] Starting sync service
2025-07-09 18:05:33.598 -07:00 [INF] Starting background sync
2025-07-09 18:05:33.636 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:05:33.646 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:05:33.647 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:05:33.647 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:05:33.648 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:05:33.650 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:05:33.654 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:05:33.655 -07:00 [WRN] No Google API settings found
2025-07-09 18:05:33.655 -07:00 [INF] Background sync completed successfully
2025-07-09 18:18:37.878 -07:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 18:18:39.056 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:18:39.127 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:18:39.131 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:18:39.154 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:18:39.175 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:18:40.326 -07:00 [INF] Starting sync service
2025-07-09 18:18:40.329 -07:00 [INF] Starting background sync
2025-07-09 18:18:40.372 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:18:40.383 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:18:40.384 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:18:40.384 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:18:40.384 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:18:40.387 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:18:40.391 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:18:40.391 -07:00 [WRN] No Google API settings found
2025-07-09 18:18:40.391 -07:00 [INF] Background sync completed successfully
