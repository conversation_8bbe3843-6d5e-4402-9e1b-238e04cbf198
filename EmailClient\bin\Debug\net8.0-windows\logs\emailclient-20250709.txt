2025-07-09 18:35:00.735 -07:00 [INF] Application starting. Logs directory: C:\Users\<USER>\dev\windows app\EmailClient\bin\Debug\net8.0-windows\logs
2025-07-09 18:35:01.364 -07:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 18:35:02.535 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 18:35:02.605 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 18:35:02.608 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 18:35:02.633 -07:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 18:35:02.653 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 18:35:03.801 -07:00 [INF] Starting sync service
2025-07-09 18:35:03.808 -07:00 [INF] Starting background sync
2025-07-09 18:35:03.852 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 18:35:03.863 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "GoogleApiSettings" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_GoogleApiSettings" PRIMARY KEY AUTOINCREMENT,
                    "ClientId" TEXT NOT NULL DEFAULT '',
                    "ClientSecret" TEXT NOT NULL DEFAULT '',
                    "AccessToken" TEXT NOT NULL DEFAULT '',
                    "RefreshToken" TEXT NOT NULL DEFAULT '',
                    "TokenExpiresAt" TEXT,
                    "Scopes" TEXT NOT NULL DEFAULT '',
                    "ContactsSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "CalendarSyncEnabled" INTEGER NOT NULL DEFAULT 1,
                    "ContactsSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "CalendarSyncIntervalMinutes" INTEGER NOT NULL DEFAULT 60,
                    "LastContactsSync" TEXT,
                    "LastCalendarSync" TEXT,
                    "ContactsSyncToken" TEXT NOT NULL DEFAULT '',
                    "CalendarSyncToken" TEXT NOT NULL DEFAULT '',
                    "IsAuthenticated" INTEGER NOT NULL DEFAULT 0,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
                );
2025-07-09 18:35:03.864 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "CalendarEvents" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_CalendarEvents" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "CalendarId" TEXT NOT NULL DEFAULT '',
                    "Summary" TEXT NOT NULL DEFAULT '',
                    "Description" TEXT NOT NULL DEFAULT '',
                    "Location" TEXT NOT NULL DEFAULT '',
                    "StartDateTime" TEXT,
                    "EndDateTime" TEXT,
                    "IsAllDay" INTEGER NOT NULL DEFAULT 0,
                    "TimeZone" TEXT NOT NULL DEFAULT '',
                    "Status" TEXT NOT NULL DEFAULT '',
                    "Visibility" TEXT NOT NULL DEFAULT '',
                    "Attendees" TEXT NOT NULL DEFAULT '',
                    "Organizer" TEXT NOT NULL DEFAULT '',
                    "Creator" TEXT NOT NULL DEFAULT '',
                    "RecurrenceRule" TEXT NOT NULL DEFAULT '',
                    "RecurringEventId" TEXT NOT NULL DEFAULT '',
                    "HtmlLink" TEXT NOT NULL DEFAULT '',
                    "HangoutLink" TEXT NOT NULL DEFAULT '',
                    "ConferenceData" TEXT NOT NULL DEFAULT '',
                    "Reminders" TEXT NOT NULL DEFAULT '',
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:35:03.864 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                CREATE TABLE IF NOT EXISTS "Contacts" (
                    "Id" INTEGER NOT NULL CONSTRAINT "PK_Contacts" PRIMARY KEY AUTOINCREMENT,
                    "GoogleId" TEXT NOT NULL DEFAULT '',
                    "DisplayName" TEXT NOT NULL DEFAULT '',
                    "GivenName" TEXT NOT NULL DEFAULT '',
                    "FamilyName" TEXT NOT NULL DEFAULT '',
                    "MiddleName" TEXT NOT NULL DEFAULT '',
                    "EmailAddresses" TEXT NOT NULL DEFAULT '',
                    "PhoneNumbers" TEXT NOT NULL DEFAULT '',
                    "Addresses" TEXT NOT NULL DEFAULT '',
                    "Organizations" TEXT NOT NULL DEFAULT '',
                    "PhotoUrl" TEXT NOT NULL DEFAULT '',
                    "Notes" TEXT NOT NULL DEFAULT '',
                    "Birthday" TEXT,
                    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "LastSyncAt" TEXT NOT NULL DEFAULT (datetime('now')),
                    "ETag" TEXT NOT NULL DEFAULT '',
                    "IsDeleted" INTEGER NOT NULL DEFAULT 0
                );
2025-07-09 18:35:03.864 -07:00 [INF] Google API tables ensured to exist
2025-07-09 18:35:03.867 -07:00 [WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-07-09 18:35:03.871 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "g"."Id", "g"."AccessToken", "g"."CalendarSyncEnabled", "g"."CalendarSyncIntervalMinutes", "g"."CalendarSyncToken", "g"."ClientId", "g"."ClientSecret", "g"."ContactsSyncEnabled", "g"."ContactsSyncIntervalMinutes", "g"."ContactsSyncToken", "g"."CreatedAt", "g"."IsAuthenticated", "g"."LastCalendarSync", "g"."LastContactsSync", "g"."RefreshToken", "g"."Scopes", "g"."TokenExpiresAt", "g"."UpdatedAt"
FROM "GoogleApiSettings" AS "g"
LIMIT 1
2025-07-09 18:35:03.913 -07:00 [WRN] Google API settings indicate not authenticated (IsAuthenticated = false)
2025-07-09 18:35:03.913 -07:00 [INF] Background sync completed successfully
2025-07-09 18:36:28.272 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "c"."Id", "c"."Addresses", "c"."Birthday", "c"."CreatedAt", "c"."DisplayName", "c"."ETag", "c"."EmailAddresses", "c"."FamilyName", "c"."GivenName", "c"."GoogleId", "c"."IsDeleted", "c"."LastSyncAt", "c"."MiddleName", "c"."Notes", "c"."Organizations", "c"."PhoneNumbers", "c"."PhotoUrl", "c"."UpdatedAt"
FROM "Contacts" AS "c"
WHERE NOT ("c"."IsDeleted")
ORDER BY "c"."DisplayName"
