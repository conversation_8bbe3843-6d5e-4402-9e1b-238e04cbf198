using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

namespace EmailClient.Views
{
    public partial class LogViewerWindow : Window
    {
        private readonly DispatcherTimer _refreshTimer;
        private string _logFilePath;
        private long _lastFileSize = 0;

        public LogViewerWindow()
        {
            InitializeComponent();
            
            // Find the most recent log file
            FindLatestLogFile();
            
            // Set up auto-refresh timer
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
            
            // Load initial content
            LoadLogContent();
        }

        private void FindLatestLogFile()
        {
            try
            {
                var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (Directory.Exists(logsDirectory))
                {
                    var logFiles = Directory.GetFiles(logsDirectory, "emailclient-*.txt")
                                          .OrderByDescending(f => File.GetLastWriteTime(f))
                                          .ToArray();
                    
                    if (logFiles.Length > 0)
                    {
                        _logFilePath = logFiles[0];
                        StatusTextBlock.Text = $"Monitoring: {Path.GetFileName(_logFilePath)}";
                    }
                    else
                    {
                        StatusTextBlock.Text = "No log files found";
                    }
                }
                else
                {
                    StatusTextBlock.Text = "Logs directory not found";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Error finding log files: {ex.Message}";
            }
        }

        private void LoadLogContent()
        {
            if (string.IsNullOrEmpty(_logFilePath) || !File.Exists(_logFilePath))
                return;

            try
            {
                var content = File.ReadAllText(_logFilePath);
                var filteredContent = ApplyFilter(content);
                
                LogTextBox.Text = filteredContent;
                _lastFileSize = new FileInfo(_logFilePath).Length;
                
                if (AutoScrollCheckBox.IsChecked == true)
                {
                    LogScrollViewer.ScrollToEnd();
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Error reading log file: {ex.Message}";
            }
        }

        private string ApplyFilter(string content)
        {
            var filter = FilterTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(filter))
                return content;

            var lines = content.Split('\n');
            var filteredLines = lines.Where(line => line.Contains(filter, StringComparison.OrdinalIgnoreCase));
            return string.Join('\n', filteredLines);
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(_logFilePath) || !File.Exists(_logFilePath))
                return;

            try
            {
                var currentFileSize = new FileInfo(_logFilePath).Length;
                if (currentFileSize != _lastFileSize)
                {
                    LoadLogContent();
                }
            }
            catch
            {
                // Ignore errors during auto-refresh
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadLogContent();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
        }

        private void FilterTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            LoadLogContent();
        }

        protected override void OnClosed(EventArgs e)
        {
            _refreshTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
