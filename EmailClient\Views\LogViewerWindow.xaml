<Window x:Class="EmailClient.Views.LogViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Log Viewer" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <Button Name="RefreshButton" Content="Refresh" Click="RefreshButton_Click" Margin="0,0,10,0"/>
            <Button Name="ClearButton" Content="Clear" Click="ClearButton_Click" Margin="0,0,10,0"/>
            <CheckBox Name="AutoScrollCheckBox" Content="Auto-scroll" IsChecked="True" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="Filter:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Name="FilterTextBox" Width="200" TextChanged="FilterTextBox_TextChanged"/>
        </StackPanel>
        
        <!-- Log Content -->
        <ScrollViewer Grid.Row="1" Name="LogScrollViewer" Margin="10,0,10,10">
            <TextBox Name="LogTextBox" 
                     IsReadOnly="True" 
                     FontFamily="Consolas" 
                     FontSize="12"
                     TextWrapping="Wrap"
                     VerticalScrollBarVisibility="Auto"
                     HorizontalScrollBarVisibility="Auto"
                     Background="Black"
                     Foreground="White"/>
        </ScrollViewer>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusTextBlock" Text="Ready"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
